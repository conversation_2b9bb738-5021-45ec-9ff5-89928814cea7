<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="onClickListener"
            type="com.dep.biguo.mvp.ui.activity.ZkLoginActivity" />
    </data>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:fillViewport="true">


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="20dp">

            <!-- 新增: 返回箭头 -->
            <ImageView
                android:id="@+id/back_arrow"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="36dp"
                android:padding="8dp"
                android:src="@drawable/arrow_back"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:src="@drawable/arrow_back" />


            <com.dep.biguo.widget.DiversificationTextView
                android:id="@+id/titleView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="23dp"
                android:layout_marginEnd="24dp"
                android:text="手机号验证码登录\n未注册的手机号验证通过后将自动注册"
                android:textColor="@color/login_color_title"
                android:textSize="24sp"
                android:textStyle="bold"
                app:changeColor="@color/tblack3"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/back_arrow"
                app:size="14sp"
                app:startChar="\n" />

            <!--
              复用: phoneView 输入框，调整样式和边距。
            -->
            <EditText
                android:id="@+id/phoneView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="40dp"
                android:layout_marginEnd="20dp"
                android:background="@drawable/bg_round_10_gray"
                android:hint="请输入手机号"
                android:inputType="phone"
                android:maxLength="11"
                android:padding="15dp"
                android:textColor="@color/tblack"
                android:textColorHint="@color/gray2"
                android:textSize="14sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/titleView" />

            <!--
              复用: verifyLayout 验证码输入区域，调整样式和边距。
            -->
            <LinearLayout
                android:id="@+id/verifyLayout"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:background="@drawable/bg_round_10_gray"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingHorizontal="16dp"
                app:layout_constraintEnd_toEndOf="@id/phoneView"
                app:layout_constraintStart_toStartOf="@id/phoneView"
                app:layout_constraintTop_toBottomOf="@id/phoneView">

                <EditText
                    android:id="@+id/verifyView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@android:color/transparent"
                    android:digits="0123456789"
                    android:hint="请输入验证码"
                    android:inputType="number"
                    android:maxLength="6"
                    android:paddingVertical="16dp"
                    android:textColor="@color/tblack"
                    android:textColorHint="@color/gray2"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/getVerifyView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:onClick="@{onClickListener}"
                    android:layout_marginTop="15dp"
                    android:layout_marginBottom="15dp"
                    android:layout_marginEnd="15dp"
                    android:text="获取验证码"
                    android:textColor="#D53E43"
                    android:textSize="14sp" />

            </LinearLayout>

            <!--
              复用: loginView 作为主登录按钮，修改样式。
            -->
            <com.biguo.utils.widget.StyleTextView
                android:id="@+id/loginView"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_marginTop="40dp"
                android:background="@drawable/selector_v2_click"
                android:gravity="center"
                android:onClick="@{onClickListener}"
                android:text="@string/login"
                android:textColor="@color/twhite"
                android:textSize="16sp"
                android:textStyle="bold"
                app:bgGradientEndColor="@color/theme"
                app:bgGradientStartColor="#FA6C64"
                app:all_round="48dp"

                app:layout_constraintEnd_toEndOf="@id/verifyLayout"
                app:layout_constraintStart_toStartOf="@id/verifyLayout"
                app:layout_constraintTop_toBottomOf="@+id/verifyLayout" />

            <!--
              复用: smsLoginView 作为 “账号密码登录” 按钮。
            -->
            <com.biguo.utils.widget.StyleTextView
                android:id="@+id/smsLoginView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:gravity="center"
                android:onClick="@{onClickListener}"
                android:padding="8dp"
                android:text="账号密码登录"
                android:textColor="@color/tblack2"
                android:textSize="14dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/loginView" />


            <!-- 新增: 第三方登录标题 -->
            <TextView
                android:id="@+id/third_party_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="88dp"
                android:text="第三方登录"
                android:textColor="@color/tblack3"
                android:textSize="12sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/smsLoginView" />

            <!-- 新增: 第三方登录图标容器 -->
            <LinearLayout
                android:id="@+id/third_party_icons"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:orientation="horizontal"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/third_party_title">

                <ImageView
                    android:id="@+id/wechat_login"
                    android:layout_width="42dp"
                    android:layout_height="42dp"
                    tools:src="@drawable/login_icon_wechat"/>

                <ImageView
                    android:id="@+id/qq_login"
                    android:layout_width="42dp"
                    android:layout_height="42dp"
                    android:layout_marginStart="32dp"
                    android:layout_marginEnd="32dp"
                    tools:src="@drawable/login_icon_qq"/>

                <ImageView
                    android:id="@+id/apple_login"
                    android:layout_width="42dp"
                    android:layout_height="42dp"
                    android:src="@drawable/login_icon_apple"
                    tools:src="@drawable/login_icon_apple"/>
            </LinearLayout>


            <ImageView
                android:id="@+id/agreement_icon"
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:layout_marginStart="52dp"
                android:layout_marginBottom="20dp"
                android:src="@drawable/login_check_button"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="同意协议"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                tools:src="@drawable/login_check_button"/>

            <!-- 协议文本 (TextView) -->
            <TextView
                android:id="@+id/agreement_text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:layout_marginEnd="52dp"
                android:text="我已阅读用户协议、隐私政策，并同意协议内容"
                android:textColor="#999999"
                android:textSize="12sp"
                app:layout_constraintBottom_toBottomOf="@id/agreement_icon"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/agreement_icon"
                app:layout_constraintTop_toTopOf="@id/agreement_icon" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>
</layout>