<?xml version="1.0" encoding="utf-8"?>
<!--
  使用DataBinding，并定义点击事件监听器变量。
-->
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="onClickListener"
            type="com.dep.biguo.mvp.ui.activity.LoginTypeActivity" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:fitsSystemWindows="true">

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="240dp"
            android:src="@drawable/login_background"
            android:scaleType="centerCrop"
            android:contentDescription="登录背景"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!--
          “随便逛逛” 文本按钮，位于屏幕右上角。
          它的右侧被约束到箭头图标的左侧。
        -->
        <TextView
            android:id="@+id/browseTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="5dp"
            android:padding="8dp"
            android:text="随便逛逛"
            android:textColor="@color/tblack3"
            android:textSize="14sp"
            app:layout_constraintEnd_toStartOf="@+id/arrow_right_imageview"
            app:layout_constraintTop_toTopOf="parent" />


        <ImageView
            android:id="@+id/arrow_right_imageview"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="15dp"
            android:src="@drawable/arrow_right"
            android:contentDescription="向右箭头"
            app:layout_constraintBottom_toBottomOf="@+id/browseTextView"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/browseTextView"
            tools:src="@drawable/arrow_right" />

        <ImageView
            android:id="@+id/logoView"
            android:layout_width="90dp"
            android:layout_height="90dp"
            android:contentDescription="@string/app_name"
            android:src="@drawable/one_key_login"
            app:layout_constraintBottom_toTopOf="@+id/phoneTextView"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            app:layout_constraintVertical_bias="0.4"/>

        <TextView
            android:id="@+id/phoneTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="60dp"
            android:text="181****1234"
            android:textColor="@color/tblack"
            android:textSize="22sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@+id/oneKeyLoginView"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/logoView"
            tools:text="181****1234" />

        <!--
          本机号码一键登录按钮:
          保留原有ID和自定义StyleTextView，修改文本内容以匹配设计。
          宽度设置为匹配约束并给定边距，使其充满大部分屏幕宽度。
        -->
        <com.biguo.utils.widget.StyleTextView
            android:id="@+id/oneKeyLoginView"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_marginStart="20dp"
            android:layout_marginTop="30dp"
            android:layout_marginEnd="20dp"
            android:gravity="center"
            android:onClick="@{onClickListener.onClick}"
            android:text="本机号码一键登录"
            android:textColor="@color/twhite"
            android:textSize="16dp"
            app:all_round="48dp"
            app:bgGradientEndColor="@color/theme"
            app:bgGradientStartColor="#FA6C64"
            app:layout_constraintBottom_toTopOf="@+id/otherLoginView"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/phoneTextView" />

        <!--
          其他登录方式: 保留ID，并约束在主按钮下方。
        -->
        <TextView
            android:id="@+id/otherLoginView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:onClick="@{onClickListener.onClick}"
            android:padding="8dp"
            android:text="其他登录方式"
            android:textColor="@color/tblack3"
            android:textSize="14sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/oneKeyLoginView" />


        <ImageView
            android:id="@+id/agreement_check_button"
            android:layout_width="14dp"
            android:layout_height="14dp"
            android:layout_marginStart="29dp"
            android:layout_marginBottom="38dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="同意协议"
            android:src="@drawable/login_check_button"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:src="@drawable/login_check_button"/>

        <TextView
            android:id="@+id/agreement_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:layout_marginEnd="29dp"
            android:gravity="center"
            android:lineSpacingExtra="4dp"
            android:text="我已阅读中国移动认证服务条款和用户协议、隐私政策并同意协议内容"
            android:textColor="#999999"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="@+id/agreement_check_button"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/agreement_check_button"
            app:layout_constraintTop_toBottomOf="@+id/agreement_check_button" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>