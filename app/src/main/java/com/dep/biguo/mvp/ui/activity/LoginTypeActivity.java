package com.dep.biguo.mvp.ui.activity;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Handler;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.LinearLayoutCompat;
import androidx.core.content.res.ResourcesCompat;
import androidx.databinding.DataBindingUtil;

import com.biguo.utils.dialog.MessageDialog;
import com.biguo.utils.util.DisplayHelper;
import com.dep.biguo.R;
import com.dep.biguo.app.EventBusTags;
import com.dep.biguo.bean.UserBean;
import com.dep.biguo.common.Constant;
import com.dep.biguo.databinding.LoginTypeActivityBinding;
import com.dep.biguo.di.component.DaggerLoginTypeComponent;
import com.dep.biguo.dialog.LoginPrivateDialog;
import com.dep.biguo.dialog.PrivateDialog;
import com.dep.biguo.mvp.contract.LoginTypeContract;
import com.dep.biguo.mvp.presenter.LoginTypePresenter;
import com.dep.biguo.qqapi.QQLoginHelper;
import com.dep.biguo.utils.MainAppUtils;
import com.dep.biguo.utils.OneKeyLoginHelper;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.widget.toolbar.NormalToolbarUtil;
import com.dep.biguo.wxapi.WXEntryActivity;
import com.hjq.toast.ToastUtils;
import com.jess.arms.base.BaseActivity;
import com.jess.arms.di.component.AppComponent;
import com.jess.arms.integration.AppManager;
import com.tencent.connect.common.Constants;
import com.umeng.analytics.MobclickAgent;

import org.simple.eventbus.EventBus;
import org.simple.eventbus.Subscriber;

public class LoginTypeActivity extends BaseActivity<LoginTypePresenter> implements LoginTypeContract.View, View.OnClickListener {
    private LoginTypeActivityBinding binding;

    private QQLoginHelper qqLoginHelper;//QQ登录

    public static void start(Context context){
        Intent intent = new Intent(context, LoginTypeActivity.class);
        context.startActivity(intent);
    }

    @Override
    public void setupActivityComponent(@NonNull AppComponent appComponent) {
        DaggerLoginTypeComponent
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);
    }

    @Override
    public int initView(@Nullable Bundle savedInstanceState) {
        binding = DataBindingUtil.setContentView(this, R.layout.login_type_activity);
        binding.setOnClickListener(this);
        new NormalToolbarUtil(this)
                .setBackgroundColor(Color.TRANSPARENT)
                .setAnchorView(binding.logoView)
                .setLeftDrawableRes(R.drawable.close);

        //Logo按比例放大
        Drawable drawable = ResourcesCompat.getDrawable(getResources(), R.drawable.one_key_login, getTheme());
        int logoWidth = computerScale(DisplayHelper.dp2px(this, 80));
        int logoHeight = computerScale(DisplayHelper.dp2px(this, 80));
        drawable.setBounds(0, 0, logoWidth, logoHeight);
        binding.logoView.setCompoundDrawablesRelative(null, drawable, null, null);
        binding.logoView.setCompoundDrawablePadding(computerScale(DisplayHelper.dp2px(this, 10)));

        //登录方式之间的间距按比例调整
        int topMargin = computerScale(DisplayHelper.dp2px(this, 20));
        ((LinearLayoutCompat.LayoutParams)binding.oneKeyLoginView.getLayoutParams()).setMargins(0, topMargin, 0, 0);
        ((LinearLayoutCompat.LayoutParams)binding.wechatLoginView.getLayoutParams()).setMargins(0, topMargin, 0, 0);
        ((LinearLayoutCompat.LayoutParams)binding.QQLoginView.getLayoutParams()).setMargins(0, topMargin, 0, 0);

        //登录方式的父布局按比例设置宽度
        int allLoginTypeLayoutWidth = computerScale(DisplayHelper.dp2px(this, 290));
        LinearLayoutCompat.LayoutParams allLoginTypeLayoutLP = new LinearLayoutCompat.LayoutParams(allLoginTypeLayoutWidth, LinearLayoutCompat.LayoutParams.WRAP_CONTENT);
        binding.allLoginTypeLayout.setLayoutParams(allLoginTypeLayoutLP);

        setPrivateAgreementText();
        return 0;
    }

    /**计算放大比例
     * @param value 原值
     * @return 按比例放大后的值
     */
    private int computerScale(int value){
        float scale = 1f * DisplayHelper.getWindowWidth(this) / DisplayHelper.dp2px(this, 480);
        int scaleValue = (int) (scale * value);
        if(scaleValue < value){
            //小于原值，就返回原值
            return value;
        }else if(scaleValue > DisplayHelper.getWindowWidth(this) - DisplayHelper.dp2px(this, 84)){
            //大于 【屏幕宽度 - 84dp】，也返回原值
            return value;
        }
        return scaleValue;
    }

    private void setPrivateAgreementText(){
        String text = binding.agreementView.getText().toString();
        SpannableString spannableString = new SpannableString(text);
        spannableString.setSpan(new ForegroundColorSpan(getResources().getColor(R.color.theme)), 7, 13, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(getResources().getColor(R.color.theme)), 14, 20, Spannable.SPAN_INCLUSIVE_INCLUSIVE);

        //添加用户协议事件
        spannableString.setSpan(new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                HtmlActivity.start(LoginTypeActivity.this, Constant.AGREEMENT_USER);
            }

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                ds.setUnderlineText(false);
            }
        }, 7, 13, Spannable.SPAN_INCLUSIVE_INCLUSIVE);

        //添加隐私政策事件
        spannableString.setSpan(new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                HtmlActivity.start(LoginTypeActivity.this, Constant.AGREEMENT_USER4);
            }

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                ds.setUnderlineText(false);
            }
        }, 14, 20, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
        //必须设置，否则无法点击
        binding.agreementView.setMovementMethod(LinkMovementMethod.getInstance());
        binding.agreementView.setText(spannableString);
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {

    }

    @Override
    public void onClick(View view) {
        if(view == binding.oneKeyLoginView){
            OneKeyLoginHelper.oneKeyLogin(this, () -> finish());

        }else if(view == binding.wechatLoginView){
            if(!binding.agreementView.isChecked()){
                new LoginPrivateDialog(this)
                        .setOnAgreeListener(v -> {
                            //获取QQ授权
                            WXEntryActivity.goAuthorize(this);
                        })
                        .show();
                return;
            }
            //获取微信授权
            WXEntryActivity.goAuthorize(this);

        }else if(view == binding.QQLoginView){
            if(!binding.agreementView.isChecked()){
                new LoginPrivateDialog(this)
                        .setOnAgreeListener(v -> {
                            //获取QQ授权
                            qqLoginHelper = new QQLoginHelper();
                            qqLoginHelper.goAuthorize(LoginTypeActivity.this, o -> mPresenter.loginByQQ(o.toString()));
                        })
                        .show();
                return;
            }
            //获取QQ授权
            qqLoginHelper = new QQLoginHelper();
            qqLoginHelper.goAuthorize(this, o -> mPresenter.loginByQQ(o.toString()));

        }else if(view == binding.otherLoginView){
            ZkLoginActivity.start(this, true);
            finish();
        }
    }

    /**获取微信授权成功
     * @param code 微信授权码
     */
    @Subscriber(tag = EventBusTags.GET_WECHAT_AUTHORIZE_SUCCESS)
    private void getWechatAuthorizeSuccess(String code) {
        //别的界面发起微信登录事件也会传到当前位置，需要判断来阻止这个事件的发放
        if (mPresenter == null) return;
        mPresenter.loginWechatMsg(code);
    }

    /**微信或QQ验证成功，但需要绑定手机号
     */
    @Override
    public void loginBindPhone(String id, String type) {
        if (TextUtils.isEmpty(id)) {
            showMessage("获取登录信息出错");
            return;
        }
        new Handler().postDelayed(() -> {
            if (AppManager.getAppManager().getCurrentActivity() instanceof LoginTypeActivity) {
                new MessageDialog.Builder(getSupportFragmentManager())
                        .setTitle("温馨提示")
                        .setContent("为了您的账号安全，请先绑定手机号码或笔果账号")
                        .setNegativeText("算了")
                        .setPositiveText("去绑定")
                        .setPositiveClickListener(v -> {
                            ZkOperatePhoneActivity.start(this, type, id);
                        })
                        .builder()
                        .show();
            }
        }, 500);
    }

    @Subscriber(tag = EventBusTags.LOGIN_WECHAT_BIND)
    public void wechatBindSuccess(UserBean user) {
        loginSuccess(user);
    }

    @Subscriber(tag = EventBusTags.LOGIN_QQ_BIND)
    public void QQBindSuccess(UserBean user) {
        loginSuccess(user);
    }

    /**登录成功/微信登录绑定手机号成功
     * @param user 用户信息
     */
    @Override
    public void loginSuccess(UserBean user) {
        MobclickAgent.onProfileSignIn(String.valueOf(user.getUser_id()));
        UserCache.cacheUser(user);
        setResult(Activity.RESULT_OK);
        showMessage("登录成功");
        MainAppUtils.loginSuccessStartSurvey();
        finish();
        EventBus.getDefault().post(user, EventBusTags.LOGIN_SUCCESS);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        if(requestCode == Constants.REQUEST_LOGIN) {
            qqLoginHelper.onActivityResult(requestCode, resultCode, data);
        }
        super.onActivityResult(requestCode, resultCode, data);
    }

    @Override
    public void showMessage(@NonNull String message) {
        ToastUtils.show(message);
    }

    @Override
    public Context getContext() {
        return this;
    }
}