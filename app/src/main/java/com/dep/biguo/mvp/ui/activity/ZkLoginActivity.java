package com.dep.biguo.mvp.ui.activity;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.os.Handler;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;

import android.text.Spannable;
import android.text.SpannableString;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.view.inputmethod.InputMethodManager;

import com.biguo.utils.util.LogUtil;
import com.dep.biguo.R;
import com.dep.biguo.app.EventBusTags;
import com.dep.biguo.bean.UserBean;
import com.dep.biguo.common.Constant;
import com.dep.biguo.databinding.ZkLoginActivityBinding;
import com.dep.biguo.di.component.DaggerZkLoginComponent;
import com.dep.biguo.dialog.LoginPrivateDialog;
import com.dep.biguo.mvp.contract.ZkLoginContract;
import com.dep.biguo.mvp.presenter.ZkLoginPresenter;
import com.dep.biguo.qqapi.QQLoginHelper;
import com.dep.biguo.utils.MainAppUtils;
import com.dep.biguo.utils.OneKeyLoginHelper;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.dialog.GetVerifyDialog;
import com.biguo.utils.dialog.MessageDialog;
import com.dep.biguo.widget.ToolBar;
import com.dep.biguo.widget.toolbar.NormalToolbarUtil;
import com.dep.biguo.wxapi.WXEntryActivity;
import com.hjq.toast.ToastUtils;
import com.jess.arms.base.BaseActivity;
import com.jess.arms.di.component.AppComponent;
import com.jess.arms.integration.AppManager;
import com.umeng.analytics.MobclickAgent;

import org.simple.eventbus.EventBus;
import org.simple.eventbus.Subscriber;

import java.util.Map;

public class ZkLoginActivity extends BaseActivity<ZkLoginPresenter> implements ZkLoginContract.View, View.OnClickListener {
    private static final String IS_SMS_LOGIN = "isSmsLogin";

    private ZkLoginActivityBinding binding;

    private static final int REGISTER = 1000;
    public static final String PHONE = "phone";
    public static final String PASSWORD = "password";

    private GetVerifyDialog verifyDialog;//图形验证码弹窗
    private boolean isAgreementChecked = false; // Track agreement checkbox state
    private QQLoginHelper qqLoginHelper;//QQ登录

    public static void start(Context context, boolean isSmsLogin){
        Intent intent = new Intent(context, ZkLoginActivity.class);
        intent.putExtra(IS_SMS_LOGIN, isSmsLogin);
        context.startActivity(intent);
    }


    @Override
    public void setupActivityComponent(@NonNull AppComponent appComponent) {
        DaggerZkLoginComponent
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);
    }

    @Override
    public int initView(@Nullable Bundle savedInstanceState) {
        binding = DataBindingUtil.setContentView(this, R.layout.zk_login_activity);
        binding.setOnClickListener(this);
        new NormalToolbarUtil(this)
                .setBackgroundColor(Color.TRANSPARENT)
                .setAnchorView(binding.titleView);

        // Setup agreement checkbox click listener
        binding.agreementIcon.setOnClickListener(v -> toggleAgreementCheckbox());

        // Setup third-party login click listeners
        binding.wechatLogin.setOnClickListener(this);
        binding.qqLogin.setOnClickListener(this);

        //设置隐私政策的显示样式
        setPrivateAgreementText();

        return 0;
    }

    /**
     * Toggle agreement checkbox state
     */
    private void toggleAgreementCheckbox() {
        isAgreementChecked = !isAgreementChecked;
        // Update checkbox image based on state
        binding.agreementIcon.setImageResource(
            isAgreementChecked ? R.drawable.login_check_button_selected : R.drawable.login_check_button
        );
    }

    /**
     * Check if agreement is checked
     */
    private boolean isAgreementChecked() {
        return isAgreementChecked;
    }

    private void setPrivateAgreementText(){
        String text = binding.agreementText.getText().toString();
        SpannableString spannableString = new SpannableString(text);
        spannableString.setSpan(new ForegroundColorSpan(getResources().getColor(R.color.theme)), 7, 13, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(getResources().getColor(R.color.theme)), 14, 20, Spannable.SPAN_INCLUSIVE_INCLUSIVE);

        //添加用户协议事件
        spannableString.setSpan(new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                HtmlActivity.start(ZkLoginActivity.this, Constant.AGREEMENT_USER);
            }

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                ds.setUnderlineText(false);
            }
        }, 7, 13, Spannable.SPAN_INCLUSIVE_INCLUSIVE);

        //添加隐私政策事件
        spannableString.setSpan(new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                HtmlActivity.start(ZkLoginActivity.this, Constant.AGREEMENT_USER4);
            }

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                ds.setUnderlineText(false);
            }
        }, 14, 20, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
        //必须设置，否则无法点击
        binding.agreementText.setMovementMethod(LinkMovementMethod.getInstance());
        binding.agreementText.setText(spannableString);
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
        // Always use SMS login since password login UI is not available
        // The layout only supports SMS login mode
    }

    // Removed changeLoginMethod since password login UI is not available in the layout
    // The current layout only supports SMS login

    @Override
    public void onClick(View view){
        if(view == binding.getVerifyView){//获取验证码
            showVerifyDialog();

        }else if(view == binding.loginView){//登录
            login(true);

        }else if(view == binding.smsLoginView){
            // Switch to password login by going to a different activity or showing a dialog
            // Since password UI is not available in current layout
            showMessage("密码登录功能暂不可用");

        }else if(view == binding.wechatLogin){
            if(!isAgreementChecked()){
                new LoginPrivateDialog(this)
                        .setOnAgreeListener(v -> {
                            //获取微信授权
                            WXEntryActivity.goAuthorize(this);
                        })
                        .show();
                return;
            }
            //获取微信授权
            WXEntryActivity.goAuthorize(this);

        }else if(view == binding.qqLogin){
            if(!isAgreementChecked()){
                new LoginPrivateDialog(this)
                        .setOnAgreeListener(v -> {
                            //获取QQ授权
                            qqLoginHelper = new QQLoginHelper();
                            qqLoginHelper.goAuthorize(ZkLoginActivity.this, o -> mPresenter.loginByQQ(o.toString()));
                        })
                        .show();
                return;
            }
            //获取QQ授权
            qqLoginHelper = new QQLoginHelper();
            qqLoginHelper.goAuthorize(this, o -> mPresenter.loginByQQ(o.toString()));
        }
    }

    /**显示图形验证码获取短信验证码的弹窗
     *
     */
    private void showVerifyDialog(){
        if(TextUtils.isEmpty(binding.phoneView.getText().toString())){
            showMessage("请先输入手机号");
            return;
        }
        //获取图片验证码弹窗时，同时弹出弹窗
        if(verifyDialog==null) {
            verifyDialog = new GetVerifyDialog(this);
            verifyDialog.setOnListener(new GetVerifyDialog.OnListener() {
                @Override
                public void refreshImageVerify() {
                    mPresenter.getImageVerifyCode(binding.phoneView.getText().toString());
                }

                @Override
                public void positive(String verify) {
                    mPresenter.getSmsVerifyCode(binding.phoneView.getText().toString(), verify);
                }
            });
        }
        verifyDialog.show();
    }

    /**登录
     *
     */
    private void login(boolean isCheckPrivate){
        InputMethodManager imm = (InputMethodManager) getSystemService(INPUT_METHOD_SERVICE);
        View decorView = getWindow().peekDecorView();
        if (null != decorView) {
            imm.hideSoftInputFromWindow(decorView.getWindowToken(), 0);
        }

        // Only SMS login is supported in current layout
        String phone = binding.phoneView.getText().toString();
        String verify = binding.verifyView.getText().toString();
        mPresenter.loginBySms(phone, verify, isCheckPrivate);
    }

    @Override
    public boolean checkAgreePrivate(){
        if(isAgreementChecked()) return true;

        new LoginPrivateDialog(this)
                .setOnAgreeListener(v -> login(false))
                .show();
        return false;
    }

    @Override
    public void setVerifyTimerText(String timerText, boolean isFinish) {
        binding.getVerifyView.setText(timerText);
        binding.getVerifyView.setClickable(isFinish);
    }

    @Override
    public void getImageVerifyCodeSuccess(String url) {
        if(verifyDialog.isShowing()) {
            verifyDialog.setImageVerify(url);
        }
    }

    @Subscriber(tag = EventBusTags.GET_WECHAT_AUTHORIZE_SUCCESS)
    private void loginByWechat(String code) {
        //别的界面发起微信登录事件也会传到当前位置，需要判断来阻止这个事件的发放
        if (mPresenter == null) return;
        mPresenter.loginWechatMsg(code);
    }

    @Subscriber(tag = EventBusTags.LOGIN_WECHAT_BIND)
    public void wechatBindSuccess(UserBean user) {
        loginSuccess(user);
    }

    @Subscriber(tag = EventBusTags.LOGIN_QQ_BIND)
    public void QQBindSuccess(UserBean user) {
        loginSuccess(user);
    }

    @Subscriber(tag = EventBusTags.BIND_WECHAT_CANCEL)
    public void cancelWechatBindLogin(Map<String, Object> map) {
        mPresenter.loginByPassword(map.get(PHONE).toString(), map.get(PASSWORD).toString(), true);
    }

    @Override
    public void loginSuccess(UserBean user) {
        LogUtil.d("dddd", user+"ddd");
        UserCache.cacheUser(user);
        MobclickAgent.onProfileSignIn(String.valueOf(user.getUser_id()));
        setResult(Activity.RESULT_OK);
        showMessage("登录成功");
        MainAppUtils.loginSuccessStartSurvey();
        finish();
        EventBus.getDefault().post(user, EventBusTags.LOGIN_SUCCESS);
    }

    @Override
    public void bindPhone(String unionid) {
        if (TextUtils.isEmpty(unionid)) {
            showMessage("获取登录信息出错");
            return;
        }
        new Handler().postDelayed(() -> {
            if (AppManager.getAppManager().getCurrentActivity() instanceof ZkLoginActivity) {
                new MessageDialog.Builder(getSupportFragmentManager())
                        .setTitle("温馨提示")
                        .setContent("为了您的账号安全，请先绑定手机号码或笔果账号")
                        .setNegativeText("算了")
                        .setPositiveText("去绑定")
                        .setPositiveClickListener(v -> {
                            ZkOperatePhoneActivity.start(this, ZkOperatePhoneActivity.BIND_WECHAT, unionid);
                        })
                        .builder()
                        .show();
            }
        }, 500);
    }


    @Override
    public Activity getActivity() {
        return this;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        if(requestCode == com.tencent.connect.common.Constants.REQUEST_LOGIN) {
            if(qqLoginHelper != null) {
                qqLoginHelper.onActivityResult(requestCode, resultCode, data);
            }
        }
        super.onActivityResult(requestCode, resultCode, data);
    }

    @Override
    public void showMessage(@NonNull String message) {
        ToastUtils.show(message);
    }
}