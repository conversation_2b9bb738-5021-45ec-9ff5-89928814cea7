<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.easefun.polyv.livecommon">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.GET_TASKS" />

    <!-- 目前仅有纯视频开播用到 -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>


    <application>

        <activity
            android:name=".ui.widget.webview.PLVSimpleUrlWebViewActivity"
            android:configChanges="orientation|keyboard|screenSize"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustResize" />

        <activity
            android:name=".module.modules.interact.PLVOutsideWebViewActivity"
            android:configChanges="orientation|keyboard|screenSize"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustResize" />

        <provider
            android:name="com.easefun.polyv.livecommon.module.utils.PLVFileProvider"
            android:authorities="${applicationId}.plvfileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>
    </application>
</manifest>
